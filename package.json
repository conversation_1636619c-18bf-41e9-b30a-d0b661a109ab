{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "@types/react-simple-maps": "^3.0.6", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.15.0", "lucide-react": "^0.344.0", "next": "^15.2.4", "next-sitemap": "^4.2.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0", "react-router-dom": "^6.22.1", "react-simple-maps": "^1.0.0", "react-tsparticles": "^2.9.3", "tailwind-merge": "^3.2.0", "tsparticles": "^2.9.3", "tsparticles-engine": "^2.9.3", "tsparticles-slim": "^2.9.3"}, "devDependencies": {"@types/node": "^20.11.19", "@types/react": "^18.2.57", "@types/react-dom": "^18.2.19", "autoprefixer": "^10.4.18", "eslint": "^8.56.0", "eslint-config-next": "^14.1.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.3.3"}}